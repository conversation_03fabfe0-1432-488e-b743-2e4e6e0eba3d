<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_name',
        'price',
        'quantity',
        'total_price',
        'po_id',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * Relationship with PO
     */
    public function po()
    {
        return $this->belongsTo(PO::class, 'po_id');
    }

    /**
     * Relationship with OrderItems
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Relationship with Supplies
     */
    public function supplies()
    {
        return $this->hasMany(Supply::class);
    }

    /**
     * Get remaining quantity to be supplied
     */
    public function getRemainingQuantityAttribute()
    {
        $suppliedQuantity = $this->supplies->sum('quantity_supplied');
        return $this->quantity - $suppliedQuantity;
    }

    /**
     * Get total supplied quantity
     */
    public function getSuppliedQuantityAttribute()
    {
        return $this->supplies->sum('quantity_supplied');
    }

    /**
     * Check if product is fully supplied
     */
    public function isFullySupplied()
    {
        return $this->remaining_quantity <= 0;
    }
}
