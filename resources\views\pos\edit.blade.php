@extends('layouts.sidebar')

@section('title', 'Edit PO')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900">Edit Purchase Order: {{ $po->po_number }}</h2>
</div>

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <form action="{{ route('pos.update', $po) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <div class="px-4 py-5 sm:p-6">
            <!-- PO Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <label for="institution_name" class="block text-sm font-medium text-gray-700">Institution Name</label>
                    <input type="text" name="institution_name" id="institution_name" value="{{ old('institution_name', $po->institution_name) }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('institution_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" name="email" id="email" value="{{ old('email', $po->email) }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone', $po->phone) }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="po_number" class="block text-sm font-medium text-gray-700">PO Number</label>
                    <input type="text" name="po_number" id="po_number" value="{{ old('po_number', $po->po_number) }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('po_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="po_date" class="block text-sm font-medium text-gray-700">PO Date</label>
                    <input type="date" name="po_date" id="po_date" value="{{ old('po_date', $po->po_date->format('Y-m-d')) }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('po_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="po_image" class="block text-sm font-medium text-gray-700">PO Image</label>
                    <input type="file" name="po_image" id="po_image" accept="image/*" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    @if($po->po_image_path)
                        <p class="mt-1 text-sm text-gray-500">
                            Current image: 
                            <a href="{{ asset('storage/' . $po->po_image_path) }}" target="_blank" class="text-blue-600 hover:text-blue-500">
                                View Current Image
                            </a>
                        </p>
                    @endif
                    @error('po_image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mb-6">
                <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                <textarea name="address" id="address" rows="3" 
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>{{ old('address', $po->address) }}</textarea>
                @error('address')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Current Products (Read-only) -->
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Current Products</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600 mb-4">
                        <i class="fas fa-info-circle mr-2"></i>
                        Products cannot be edited after PO creation. To modify products, create a new PO.
                    </p>
                    
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-100">
                                <tr>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Product</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                                    <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Total</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($po->products as $product)
                                    <tr>
                                        <td class="px-4 py-2 text-sm text-gray-900">{{ $product->product_name }}</td>
                                        <td class="px-4 py-2 text-sm text-gray-900">${{ number_format($product->price, 2) }}</td>
                                        <td class="px-4 py-2 text-sm text-gray-900">{{ $product->quantity }}</td>
                                        <td class="px-4 py-2 text-sm text-gray-900">${{ number_format($product->total_price, 2) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                            <tfoot class="bg-gray-50">
                                <tr>
                                    <td colspan="3" class="px-4 py-2 text-sm font-medium text-gray-900">Total</td>
                                    <td class="px-4 py-2 text-sm font-medium text-gray-900">${{ number_format($po->total_value, 2) }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
            <a href="{{ route('pos.show', $po) }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2">
                Cancel
            </a>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update PO
            </button>
        </div>
    </form>
</div>

<div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-exclamation-triangle text-yellow-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">Important Notes</h3>
            <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>Only PO details can be edited, not the products</li>
                    <li>If you need to modify products, create a new PO</li>
                    <li>Existing orders from this PO will not be affected</li>
                    <li>Changes will be reflected in future orders</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
