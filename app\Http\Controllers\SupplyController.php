<?php

namespace App\Http\Controllers;

use App\Models\Supply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SupplyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $query = Supply::with(['order.po', 'product']);

        // Filter by user if not admin
        if (!$user->isAdmin()) {
            $query->whereHas('order', function($q) use ($user) {
                $q->where('user_id', $user->id);
            });
        }

        // Filter by order if specified
        if ($request->has('order')) {
            $query->where('order_id', $request->order);
        }

        // Filter by status if specified
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        $supplies = $query->latest()->paginate(10);

        return view('supplies.index', compact('supplies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Supplies are created automatically when orders are created
        return redirect()->route('supplies.index');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Supplies are created automatically when orders are created
        return redirect()->route('supplies.index');
    }

    /**
     * Display the specified resource.
     */
    public function show(Supply $supply)
    {
        // Check if user can view this supply
        if (!auth()->user()->isAdmin() && auth()->id() !== $supply->order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $supply->load(['order.po', 'product']);

        return view('supplies.show', compact('supply'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Supply $supply)
    {
        // Check if user can edit this supply
        if (!auth()->user()->isAdmin() && auth()->id() !== $supply->order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $supply->load(['order.po', 'product']);

        return view('supplies.edit', compact('supply'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Supply $supply)
    {
        // Check if user can update this supply
        if (!auth()->user()->isAdmin() && auth()->id() !== $supply->order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'dc_number' => 'nullable|string|max:255',
            'dc_date' => 'nullable|date',
            'dc_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'invoice_number' => 'nullable|string|max:255',
            'invoice_date' => 'nullable|date',
            'invoice_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $data = $request->only(['dc_number', 'dc_date', 'invoice_number', 'invoice_date']);

        // Handle DC image upload
        if ($request->hasFile('dc_image')) {
            if ($supply->dc_image_path) {
                Storage::disk('public')->delete($supply->dc_image_path);
            }
            $data['dc_image_path'] = $request->file('dc_image')->store('dc_images', 'public');
        }

        // Handle Invoice image upload
        if ($request->hasFile('invoice_image')) {
            if ($supply->invoice_image_path) {
                Storage::disk('public')->delete($supply->invoice_image_path);
            }
            $data['invoice_image_path'] = $request->file('invoice_image')->store('invoice_images', 'public');
        }

        $supply->update($data);

        return redirect()->route('supplies.show', $supply)->with('success', 'Supply updated successfully!');
    }

    /**
     * Update supply status
     */
    public function updateStatus(Request $request, Supply $supply)
    {
        // Check if user can update this supply
        if (!auth()->user()->isAdmin() && auth()->id() !== $supply->order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'status' => 'required|in:pending,issued',
        ]);

        $supply->update(['status' => $request->status]);

        return redirect()->back()->with('success', 'Supply status updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Supply $supply)
    {
        // Check if user can delete this supply
        if (!auth()->user()->isAdmin() && auth()->id() !== $supply->order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        // Delete associated images
        if ($supply->dc_image_path) {
            Storage::disk('public')->delete($supply->dc_image_path);
        }
        if ($supply->invoice_image_path) {
            Storage::disk('public')->delete($supply->invoice_image_path);
        }

        $supply->delete();

        return redirect()->route('supplies.index')->with('success', 'Supply deleted successfully!');
    }
}
