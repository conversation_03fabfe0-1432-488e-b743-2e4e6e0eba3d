@extends('layouts.sidebar')

@section('title', 'Edit Supply')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900">Edit Supply Details</h2>
    <p class="text-gray-600">Update DC and Invoice information for {{ $supply->product->product_name }}</p>
</div>

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <form action="{{ route('supplies.update', $supply) }}" method="POST" enctype="multipart/form-data">
        @csrf
        @method('PUT')
        
        <div class="px-4 py-5 sm:p-6">
            <!-- Supply Information (Read-only) -->
            <div class="mb-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Supply Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Product</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $supply->product->product_name }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Quantity Supplied</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $supply->quantity_supplied }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Total Value</label>
                        <p class="mt-1 text-sm text-gray-900">${{ number_format($supply->value, 2) }}</p>
                    </div>
                </div>
            </div>

            <!-- DC Information -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Delivery Challan (DC) Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="dc_number" class="block text-sm font-medium text-gray-700">DC Number</label>
                        <input type="text" name="dc_number" id="dc_number" value="{{ old('dc_number', $supply->dc_number) }}" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter DC number">
                        @error('dc_number')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="dc_date" class="block text-sm font-medium text-gray-700">DC Date</label>
                        <input type="date" name="dc_date" id="dc_date" value="{{ old('dc_date', $supply->dc_date ? $supply->dc_date->format('Y-m-d') : '') }}" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        @error('dc_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="dc_image" class="block text-sm font-medium text-gray-700">DC Image</label>
                        <input type="file" name="dc_image" id="dc_image" accept="image/*" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        @if($supply->dc_image_path)
                            <p class="mt-1 text-sm text-gray-500">
                                Current image: 
                                <a href="{{ asset('storage/' . $supply->dc_image_path) }}" target="_blank" class="text-blue-600 hover:text-blue-500">
                                    View Current DC Image
                                </a>
                            </p>
                        @endif
                        @error('dc_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Invoice Information -->
            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Invoice Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="invoice_number" class="block text-sm font-medium text-gray-700">Invoice Number</label>
                        <input type="text" name="invoice_number" id="invoice_number" value="{{ old('invoice_number', $supply->invoice_number) }}" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Enter invoice number">
                        @error('invoice_number')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="invoice_date" class="block text-sm font-medium text-gray-700">Invoice Date</label>
                        <input type="date" name="invoice_date" id="invoice_date" value="{{ old('invoice_date', $supply->invoice_date ? $supply->invoice_date->format('Y-m-d') : '') }}" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        @error('invoice_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div class="md:col-span-2">
                        <label for="invoice_image" class="block text-sm font-medium text-gray-700">Invoice Image</label>
                        <input type="file" name="invoice_image" id="invoice_image" accept="image/*" 
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                        @if($supply->invoice_image_path)
                            <p class="mt-1 text-sm text-gray-500">
                                Current image: 
                                <a href="{{ asset('storage/' . $supply->invoice_image_path) }}" target="_blank" class="text-blue-600 hover:text-blue-500">
                                    View Current Invoice Image
                                </a>
                            </p>
                        @endif
                        @error('invoice_image')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Current Status -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Current Status</h3>
                <div class="p-4 bg-gray-50 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Supply Status:</p>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $supply->status === 'issued' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ ucfirst($supply->status) }}
                            </span>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Last Updated:</p>
                            <p class="text-sm text-gray-900">{{ $supply->updated_at->format('M d, Y H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
            <a href="{{ route('supplies.show', $supply) }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2">
                Cancel
            </a>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update Supply
            </button>
        </div>
    </form>
</div>

<div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">Supply Update Notes</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>You can update DC and Invoice information for this supply</li>
                    <li>Product details and quantity cannot be changed</li>
                    <li>Upload images for DC and Invoice documents</li>
                    <li>Status can be changed from the supply details page</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
