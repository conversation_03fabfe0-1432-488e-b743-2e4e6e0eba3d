@extends('layouts.sidebar')

@section('title', 'Admin Dashboard')

@section('content')
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Stats Cards -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-users text-2xl text-blue-500"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $stats['total_users'] }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-file-invoice text-2xl text-green-500"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total POs</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $stats['total_pos'] }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-shopping-cart text-2xl text-yellow-500"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Orders</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $stats['total_orders'] }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-truck text-2xl text-purple-500"></i>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-500 truncate">Total Supplies</dt>
                        <dd class="text-lg font-medium text-gray-900">{{ $stats['total_supplies'] }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- Supply Status -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Supply Status</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Pending</span>
                    <span class="text-sm font-medium text-red-600">{{ $stats['pending_supplies'] }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Issued</span>
                    <span class="text-sm font-medium text-green-600">{{ $stats['issued_supplies'] }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Status -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Payment Status</h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Pending</span>
                    <span class="text-sm font-medium text-red-600">${{ number_format($stats['pending_payments'], 2) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-sm text-gray-600">Received</span>
                    <span class="text-sm font-medium text-green-600">${{ number_format($stats['received_payments'], 2) }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Payments -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="p-5">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Total Payments</h3>
            <div class="text-2xl font-bold text-blue-600">
                ${{ number_format($stats['total_payments'], 2) }}
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Recent POs -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Recent POs</h3>
            <div class="space-y-3">
                @forelse($recent_pos as $po)
                    <div class="flex justify-between items-center py-2 border-b border-gray-200">
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ $po->po_number }}</p>
                            <p class="text-xs text-gray-500">{{ $po->institution_name }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-900">${{ number_format($po->total_value, 2) }}</p>
                            <p class="text-xs text-gray-500">{{ $po->created_at->format('M d, Y') }}</p>
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-sm">No POs found.</p>
                @endforelse
            </div>
            <div class="mt-4">
                <a href="{{ route('pos.index') }}" class="text-blue-600 hover:text-blue-500 text-sm font-medium">View all POs →</a>
            </div>
        </div>
    </div>

    <!-- Pending Supplies -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Pending Supplies</h3>
            <div class="space-y-3">
                @forelse($pending_supplies as $supply)
                    <div class="flex justify-between items-center py-2 border-b border-gray-200">
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ $supply->product->product_name }}</p>
                            <p class="text-xs text-gray-500">PO: {{ $supply->order->po->po_number }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-900">Qty: {{ $supply->quantity_supplied }}</p>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                Pending
                            </span>
                        </div>
                    </div>
                @empty
                    <p class="text-gray-500 text-sm">No pending supplies.</p>
                @endforelse
            </div>
            <div class="mt-4">
                <a href="{{ route('supplies.index') }}" class="text-blue-600 hover:text-blue-500 text-sm font-medium">View all supplies →</a>
            </div>
        </div>
    </div>
</div>
@endsection
