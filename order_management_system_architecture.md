# Order Management System Architecture

## 1. System Overview

The system will be a web application built with <PERSON><PERSON>, utilizing Blade for the frontend, MySQL for the database, and Dompdf for generating invoices and delivery challans (DCs). The system will have two main user roles:

*   **Admin:** Manages users, views all data, and has access to all features.
*   **User:** Can add POs, create orders from POs, manage supplies, view statuses, and manage payments.

## 2. Core Components

*   **Frontend (Blade & Tailwind CSS):**
    *   User Interface: Built using Blade templates and Tailwind CSS for styling and visual appeal.
    *   Navigation: Side navigation bar with icons for easy access to different sections.
    *   Dashboard: Displays key metrics and statuses for both admin and users.
    *   Forms: For adding POs, creating orders, and managing payments.
    *   Data Display: Tables and lists to display data clearly.
    *   Image Display: Ability to view uploaded images (PO images, DC images, invoice images) in a browser.
*   **Backend (Laravel):**
    *   **Authentication & Authorization:**
        *   <PERSON><PERSON>'s built-in authentication system for user registration, login, and logout.
        *   Role-based access control (RBAC) to manage user permissions (admin vs. user).
    *   **Controllers:**
        *   Controllers to handle user authentication, PO management, order processing, supply management, payment processing, and dashboard data.
    *   **Models:**
        *   Eloquent models to interact with the MySQL database.
        *   Models for Users, POs, Products, Orders, OrderItems, Supplies, DCs, Invoices, Payments, etc.
    *   **Database:**
        *   MySQL database to store all application data.
        *   Database migrations to define the database schema.
    *   **Services:**
        *   Services to encapsulate business logic (e.g., order processing, invoice generation).
    *   **Dompdf Integration:**
        *   Integration with Dompdf to generate PDFs for DCs and invoices.
    *   **Image Handling:**
        *   File storage (e.g., using Laravel's built-in file storage) to store uploaded images.
        *   Image display functionality to show images in the browser.

## 3. Database Schema (Conceptual)

```mermaid
erDiagram
    Users {
        int id PK
        string name
        string email
        string password
        string role "admin" or "user"
        timestamp created_at
        timestamp updated_at
    }
    POs {
        int id PK
        int user_id FK
        string institution_name
        string address
        string email
        string phone
        string po_number
        date po_date
        string po_image_path
        timestamp created_at
        timestamp updated_at
    }
    Products {
        int id PK
        string product_name
        decimal price
        int quantity
        decimal total_price
        int po_id FK
        timestamp created_at
        timestamp updated_at
    }
    Orders {
        int id PK
        int user_id FK
        int po_id FK
        timestamp created_at
        timestamp updated_at
    }
    OrderItems {
        int id PK
        int order_id FK
        int product_id FK
        int quantity_supplied
        decimal price
        decimal total_price
        timestamp created_at
        timestamp updated_at
    }
    Supplies {
        int id PK
        int order_id FK
        int product_id FK
        int quantity_supplied
        string dc_number
        date dc_date
        string dc_image_path
        string invoice_number
        date invoice_date
        string invoice_image_path
        enum status "issued", "pending"
        timestamp created_at
        timestamp updated_at
    }
    Payments {
        int id PK
        int order_id FK
        decimal amount
        enum status "received", "pending"
        string cheque_number
        string cheque_image_path
        timestamp created_at
        timestamp updated_at
    }
    Users ||--o{ POs : "has"
    POs ||--o{ Products : "contains"
    POs ||--o{ Orders : "generates"
    Orders ||--o{ OrderItems : "includes"
    Orders ||--o{ Payments : "related to"
    OrderItems ||--o{ Supplies : "is part of"
```

## 4. User Interface (UI) Design (Conceptual)

*   **Login/Registration:** Standard Laravel authentication views.
*   **Admin Dashboard:**
    *   Summary of key metrics (e.g., total POs, orders, payments).
    *   User management section (add, edit, delete users).
*   **User Dashboard:**
    *   Summary of key metrics (POs received, orders supplied, supply status, payment status).
*   **Side Navigation Bar:**
    *   Dashboard (with icons)
    *   PO Received (with icons)
    *   Order Supplied (with icons)
    *   Supplied Status (with icons)
    *   Payments (with icons)
*   **PO Received Page:**
    *   Form to add PO details (institution name, address, email, phone, PO number, PO date, PO image).
    *   Form to add multiple items (product name, price, quantity).
*   **Order Supplied Page:**
    *   List of POs.
    *   Selection of PO.
    *   Display of PO items.
    *   Option to select items to supply (partially or fully).
    *   Create DC and invoice for selected supply.
*   **Supplied Status Page:**
    *   Table to display DC and invoice status (issued or pending).
    *   Ability to upload DC and invoice images.
*   **Payments Page:**
    *   Table to display payment status (received or pending).
    *   Ability to upload cheque number and cheque image.
*   **Image Viewing:**
    *   When clicking on an image (PO, DC, invoice, cheque), it opens in a new tab or modal for viewing.

## 5. Workflow

1.  **Admin:**
    *   Registers and manages users.
    *   Views all data and statuses.
2.  **User:**
    *   Logs in.
    *   Adds a new PO with details and items.
    *   Selects a PO and creates an order, specifying items to supply.
    *   Generates DCs and invoices for the supplied items.
    *   Uploads DC and invoice images.
    *   Manages payments, uploads cheque details.
    *   Views dashboard with statuses.

## 6. Technology Stack

*   **Backend:** Laravel (PHP framework)
*   **Database:** MySQL
*   **Frontend:** Blade templates, Tailwind CSS, JavaScript (for any dynamic UI elements)
*   **PDF Generation:** Dompdf
*   **Image Storage:** Laravel's built-in file storage (e.g., using the `public` disk)

## 7. Implementation Steps (High-Level)

1.  **Project Setup:**
    *   Create a new Laravel project.
    *   Configure the database connection.
    *   Set up the basic directory structure.
2.  **Authentication & Authorization:**
    *   Implement user registration and login using Laravel's built-in authentication.
    *   Implement role-based access control (RBAC) for admin and user roles.
3.  **Database Schema:**
    *   Create database migrations to define the tables (Users, POs, Products, Orders, OrderItems, Supplies, Payments).
    *   Run the migrations to create the tables in the database.
4.  **Models:**
    *   Create Eloquent models for each table.
5.  **Controllers:**
    *   Create controllers for authentication, PO management, order processing, supply management, payment processing, and dashboard data.
6.  **Views (Blade Templates):**
    *   Create Blade templates for the frontend UI (login, registration, dashboard, PO forms, order forms, status pages, etc.).
    *   Use Tailwind CSS for styling.
    *   Implement the side navigation bar.
7.  **Routes:**
    *   Define routes for all the application's endpoints.
8.  **Services:**
    *   Create services to encapsulate business logic (e.g., order processing, invoice generation).
9.  **Dompdf Integration:**
    *   Install Dompdf.
    *   Implement the functionality to generate PDFs for DCs and invoices.
10. **Image Handling:**
    *   Implement file storage for uploaded images.
    *   Implement image display functionality.
11. **Testing:**
    *   Write unit and feature tests to ensure the application functions correctly.
12. **Deployment:**
    *   Deploy the application to a production server.

## 8. Security Considerations

*   **Input Validation:** Validate all user inputs to prevent security vulnerabilities.
*   **Authentication & Authorization:** Implement robust authentication and authorization mechanisms.
*   **Data Encryption:** Encrypt sensitive data (e.g., passwords, payment information).
*   **Regular Security Audits:** Conduct regular security audits to identify and address vulnerabilities.

## 9. Scalability and Performance

*   **Database Optimization:** Optimize database queries and indexes.
*   **Caching:** Implement caching to improve performance.
*   **Load Balancing:** Use load balancing to distribute traffic across multiple servers.
*   **Asynchronous Tasks:** Use queues to handle time-consuming tasks asynchronously (e.g., PDF generation, image processing).