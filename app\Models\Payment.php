<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'amount',
        'status',
        'cheque_number',
        'cheque_image_path',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    /**
     * Relationship with Order
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Check if payment is received
     */
    public function isReceived()
    {
        return $this->status === 'received';
    }

    /**
     * Check if payment is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Mark payment as received
     */
    public function markAsReceived()
    {
        $this->update(['status' => 'received']);
    }

    /**
     * Mark payment as pending
     */
    public function markAsPending()
    {
        $this->update(['status' => 'pending']);
    }
}
