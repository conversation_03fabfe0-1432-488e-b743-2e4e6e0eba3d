<?php

namespace App\Http\Controllers;

use App\Models\PO;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class POController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();

        if ($user->isAdmin()) {
            $pos = PO::with(['user', 'products'])->latest()->paginate(10);
        } else {
            $pos = $user->pos()->with('products')->latest()->paginate(10);
        }

        return view('pos.index', compact('pos'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('pos.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'institution_name' => 'required|string|max:255',
            'address' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'po_number' => 'required|string|max:255|unique:pos',
            'po_date' => 'required|date',
            'po_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'products' => 'required|array|min:1',
            'products.*.product_name' => 'required|string|max:255',
            'products.*.price' => 'required|numeric|min:0',
            'products.*.quantity' => 'required|integer|min:1',
        ]);

        $poImagePath = null;
        if ($request->hasFile('po_image')) {
            $poImagePath = $request->file('po_image')->store('po_images', 'public');
        }

        $po = PO::create([
            'user_id' => auth()->id(),
            'institution_name' => $request->institution_name,
            'address' => $request->address,
            'email' => $request->email,
            'phone' => $request->phone,
            'po_number' => $request->po_number,
            'po_date' => $request->po_date,
            'po_image_path' => $poImagePath,
        ]);

        foreach ($request->products as $productData) {
            $totalPrice = $productData['price'] * $productData['quantity'];

            Product::create([
                'po_id' => $po->id,
                'product_name' => $productData['product_name'],
                'price' => $productData['price'],
                'quantity' => $productData['quantity'],
                'total_price' => $totalPrice,
            ]);
        }

        return redirect()->route('pos.index')->with('success', 'PO created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(PO $po)
    {
        // Check if user can view this PO
        if (!auth()->user()->isAdmin() && auth()->id() !== $po->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $po->load(['products', 'orders.orderItems', 'user']);

        return view('pos.show', compact('po'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PO $po)
    {
        // Check if user can edit this PO
        if (!auth()->user()->isAdmin() && auth()->id() !== $po->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $po->load('products');

        return view('pos.edit', compact('po'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PO $po)
    {
        // Check if user can update this PO
        if (!auth()->user()->isAdmin() && auth()->id() !== $po->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'institution_name' => 'required|string|max:255',
            'address' => 'required|string',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'po_number' => 'required|string|max:255|unique:pos,po_number,' . $po->id,
            'po_date' => 'required|date',
            'po_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $poImagePath = $po->po_image_path;
        if ($request->hasFile('po_image')) {
            if ($poImagePath) {
                Storage::disk('public')->delete($poImagePath);
            }
            $poImagePath = $request->file('po_image')->store('po_images', 'public');
        }

        $po->update([
            'institution_name' => $request->institution_name,
            'address' => $request->address,
            'email' => $request->email,
            'phone' => $request->phone,
            'po_number' => $request->po_number,
            'po_date' => $request->po_date,
            'po_image_path' => $poImagePath,
        ]);

        return redirect()->route('pos.show', $po)->with('success', 'PO updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PO $po)
    {
        // Check if user can delete this PO
        if (!auth()->user()->isAdmin() && auth()->id() !== $po->user_id) {
            abort(403, 'Unauthorized access.');
        }

        if ($po->po_image_path) {
            Storage::disk('public')->delete($po->po_image_path);
        }

        $po->delete();

        return redirect()->route('pos.index')->with('success', 'PO deleted successfully!');
    }
}
