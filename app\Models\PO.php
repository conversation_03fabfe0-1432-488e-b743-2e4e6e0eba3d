<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PO extends Model
{
    use HasFactory;

    protected $table = 'pos';

    protected $fillable = [
        'user_id',
        'institution_name',
        'address',
        'email',
        'phone',
        'po_number',
        'po_date',
        'po_image_path',
    ];

    protected $casts = [
        'po_date' => 'date',
    ];

    /**
     * Relationship with User
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relationship with Products
     */
    public function products()
    {
        return $this->hasMany(Product::class, 'po_id');
    }

    /**
     * Relationship with Orders
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get total value of PO
     */
    public function getTotalValueAttribute()
    {
        return $this->products->sum('total_price');
    }

    /**
     * Get total quantity of items in PO
     */
    public function getTotalQuantityAttribute()
    {
        return $this->products->sum('quantity');
    }
}
