<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Supply extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'product_id',
        'quantity_supplied',
        'dc_number',
        'dc_date',
        'dc_image_path',
        'invoice_number',
        'invoice_date',
        'invoice_image_path',
        'status',
    ];

    protected $casts = [
        'dc_date' => 'date',
        'invoice_date' => 'date',
    ];

    /**
     * Relationship with Order
     */
    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Relationship with Product
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Check if supply is issued
     */
    public function isIssued()
    {
        return $this->status === 'issued';
    }

    /**
     * Check if supply is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Mark supply as issued
     */
    public function markAsIssued()
    {
        $this->update(['status' => 'issued']);
    }

    /**
     * Mark supply as pending
     */
    public function markAsPending()
    {
        $this->update(['status' => 'pending']);
    }

    /**
     * Get supply value
     */
    public function getValueAttribute()
    {
        return $this->quantity_supplied * $this->product->price;
    }
}
