<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'Laravel')); ?> - Order Management System</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
        
        <!-- Font Awesome for icons -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    </head>
    <body class="font-sans antialiased bg-gray-100">
        <div class="flex h-screen">
            <!-- Sidebar -->
            <div class="w-64 bg-gray-800 text-white">
                <div class="p-4">
                    <h2 class="text-xl font-bold">Order Management</h2>
                    <p class="text-sm text-gray-300"><?php echo e(auth()->user()->name); ?> (<?php echo e(ucfirst(auth()->user()->role)); ?>)</p>
                </div>
                
                <nav class="mt-8">
                    <a href="<?php echo e(route('dashboard')); ?>" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white <?php echo e(request()->routeIs('dashboard') ? 'bg-gray-700 text-white' : ''); ?>">
                        <i class="fas fa-tachometer-alt mr-3"></i>
                        Dashboard
                    </a>
                    
                    <a href="<?php echo e(route('pos.index')); ?>" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white <?php echo e(request()->routeIs('pos.*') ? 'bg-gray-700 text-white' : ''); ?>">
                        <i class="fas fa-file-invoice mr-3"></i>
                        PO Received
                    </a>
                    
                    <a href="<?php echo e(route('orders.index')); ?>" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white <?php echo e(request()->routeIs('orders.*') ? 'bg-gray-700 text-white' : ''); ?>">
                        <i class="fas fa-shopping-cart mr-3"></i>
                        Order Supplied
                    </a>
                    
                    <a href="<?php echo e(route('supplies.index')); ?>" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white <?php echo e(request()->routeIs('supplies.*') ? 'bg-gray-700 text-white' : ''); ?>">
                        <i class="fas fa-truck mr-3"></i>
                        Supply Status
                    </a>
                    
                    <a href="<?php echo e(route('payments.index')); ?>" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white <?php echo e(request()->routeIs('payments.*') ? 'bg-gray-700 text-white' : ''); ?>">
                        <i class="fas fa-credit-card mr-3"></i>
                        Payments
                    </a>
                    
                    <?php if(auth()->user()->isAdmin()): ?>
                        <div class="border-t border-gray-700 mt-4 pt-4">
                            <p class="px-4 text-xs text-gray-400 uppercase tracking-wider">Admin</p>
                            <a href="<?php echo e(route('admin.users.index')); ?>" class="flex items-center px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white <?php echo e(request()->routeIs('admin.users.*') ? 'bg-gray-700 text-white' : ''); ?>">
                                <i class="fas fa-users mr-3"></i>
                                Manage Users
                            </a>
                        </div>
                    <?php endif; ?>
                </nav>
                
                <!-- Logout -->
                <div class="absolute bottom-0 w-64 p-4">
                    <form method="POST" action="<?php echo e(route('logout')); ?>">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="flex items-center w-full px-4 py-2 text-gray-300 hover:bg-gray-700 hover:text-white rounded">
                            <i class="fas fa-sign-out-alt mr-3"></i>
                            Logout
                        </button>
                    </form>
                </div>
            </div>

            <!-- Main Content -->
            <div class="flex-1 flex flex-col overflow-hidden">
                <!-- Top Navigation -->
                <header class="bg-white shadow-sm border-b border-gray-200">
                    <div class="px-6 py-4">
                        <h1 class="text-2xl font-semibold text-gray-900">
                            <?php echo $__env->yieldContent('title', 'Dashboard'); ?>
                        </h1>
                    </div>
                </header>

                <!-- Page Content -->
                <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-6">
                    <!-- Flash Messages -->
                    <?php if(session('success')): ?>
                        <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline"><?php echo e(session('success')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if(session('error')): ?>
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline"><?php echo e(session('error')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php echo $__env->yieldContent('content'); ?>
                </main>
            </div>
        </div>
    </body>
</html>
<?php /**PATH F:\Xampp\htdocs\new_order\resources\views/layouts/sidebar.blade.php ENDPATH**/ ?>