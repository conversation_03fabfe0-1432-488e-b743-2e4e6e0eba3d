<?php $__env->startSection('title', 'Create Order from PO'); ?>

<?php $__env->startSection('content'); ?>
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900">Create Order from PO: <?php echo e($po->po_number); ?></h2>
    <p class="text-gray-600"><?php echo e($po->institution_name); ?></p>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Order Form -->
    <div class="lg:col-span-2">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <form action="<?php echo e(route('orders.store')); ?>" method="POST" id="order-form">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="po_id" value="<?php echo e($po->id); ?>">
                
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Select Products to Supply</h3>
                    
                    <?php if($errors->any()): ?>
                        <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                            <ul class="list-disc list-inside">
                                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li><?php echo e($error); ?></li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <div class="space-y-4">
                        <?php $__currentLoopData = $po->products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <div>
                                        <h4 class="text-sm font-medium text-gray-900"><?php echo e($product->product_name); ?></h4>
                                        <p class="text-sm text-gray-500">
                                            Price: $<?php echo e(number_format($product->price, 2)); ?> | 
                                            Total Quantity: <?php echo e($product->quantity); ?> | 
                                            Remaining: <?php echo e($product->remaining_quantity); ?>

                                        </p>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="checkbox" 
                                               id="product_<?php echo e($product->id); ?>" 
                                               name="selected_products[]" 
                                               value="<?php echo e($product->id); ?>"
                                               class="product-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                               onchange="toggleProductInput(<?php echo e($product->id); ?>)">
                                        <label for="product_<?php echo e($product->id); ?>" class="ml-2 text-sm text-gray-700">Select</label>
                                    </div>
                                </div>
                                
                                <div id="quantity_input_<?php echo e($product->id); ?>" class="hidden">
                                    <label for="quantity_<?php echo e($product->id); ?>" class="block text-sm font-medium text-gray-700">Quantity to Supply</label>
                                    <input type="number" 
                                           id="quantity_<?php echo e($product->id); ?>"
                                           name="items[<?php echo e($index); ?>][quantity_supplied]" 
                                           min="1" 
                                           max="<?php echo e($product->remaining_quantity); ?>"
                                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                           placeholder="Enter quantity">
                                    <input type="hidden" name="items[<?php echo e($index); ?>][product_id]" value="<?php echo e($product->id); ?>">
                                    <p class="mt-1 text-xs text-gray-500">Maximum available: <?php echo e($product->remaining_quantity); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                    <a href="<?php echo e(route('pos.show', $po)); ?>" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        Create Order
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- PO Summary -->
    <div class="lg:col-span-1">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">PO Summary</h3>
            </div>
            <div class="border-t border-gray-200 px-4 py-5">
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">PO Number:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($po->po_number); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">PO Date:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($po->po_date->format('M d, Y')); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Products:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($po->products->count()); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Value:</span>
                        <span class="text-sm font-medium text-gray-900">$<?php echo e(number_format($po->total_value, 2)); ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Order Summary</h3>
            </div>
            <div class="border-t border-gray-200 px-4 py-5">
                <div id="order-summary" class="space-y-3">
                    <p class="text-sm text-gray-500">Select products to see order summary</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleProductInput(productId) {
    const checkbox = document.getElementById(`product_${productId}`);
    const quantityInput = document.getElementById(`quantity_input_${productId}`);
    const quantityField = document.getElementById(`quantity_${productId}`);
    
    if (checkbox.checked) {
        quantityInput.classList.remove('hidden');
        quantityField.required = true;
    } else {
        quantityInput.classList.add('hidden');
        quantityField.required = false;
        quantityField.value = '';
    }
    
    updateOrderSummary();
}

function updateOrderSummary() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    const summaryDiv = document.getElementById('order-summary');
    
    if (checkboxes.length === 0) {
        summaryDiv.innerHTML = '<p class="text-sm text-gray-500">Select products to see order summary</p>';
        return;
    }
    
    let totalItems = 0;
    let totalValue = 0;
    let summaryHTML = '';
    
    checkboxes.forEach(checkbox => {
        const productId = checkbox.value;
        const quantityField = document.getElementById(`quantity_${productId}`);
        const quantity = parseInt(quantityField.value) || 0;
        
        if (quantity > 0) {
            totalItems += quantity;
            // You would need to pass product prices to calculate total value
            // For now, we'll just show item count
        }
    });
    
    summaryHTML = `
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Selected Products:</span>
            <span class="text-sm font-medium text-gray-900">${checkboxes.length}</span>
        </div>
        <div class="flex justify-between">
            <span class="text-sm text-gray-600">Total Items:</span>
            <span class="text-sm font-medium text-gray-900">${totalItems}</span>
        </div>
    `;
    
    summaryDiv.innerHTML = summaryHTML;
}

// Add event listeners to quantity inputs
document.addEventListener('DOMContentLoaded', function() {
    const quantityInputs = document.querySelectorAll('input[type="number"]');
    quantityInputs.forEach(input => {
        input.addEventListener('input', updateOrderSummary);
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH F:\Xampp\htdocs\new_order\resources\views/orders/create-from-po.blade.php ENDPATH**/ ?>