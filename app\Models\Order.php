<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'po_id',
    ];

    /**
     * Relationship with User
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relationship with PO
     */
    public function po()
    {
        return $this->belongsTo(PO::class);
    }

    /**
     * Relationship with OrderItems
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Relationship with Supplies
     */
    public function supplies()
    {
        return $this->hasMany(Supply::class);
    }

    /**
     * Relationship with Payments
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get total order value
     */
    public function getTotalValueAttribute()
    {
        return $this->orderItems->sum('total_price');
    }

    /**
     * Get total quantity ordered
     */
    public function getTotalQuantityAttribute()
    {
        return $this->orderItems->sum('quantity_supplied');
    }

    /**
     * Get total payment amount
     */
    public function getTotalPaymentAttribute()
    {
        return $this->payments->where('status', 'received')->sum('amount');
    }

    /**
     * Get pending payment amount
     */
    public function getPendingPaymentAttribute()
    {
        return $this->total_value - $this->total_payment;
    }
}
