[2025-05-27 14:32:44] local.ERROR: SQLSTATE[HY000] [2054] The server requested authentication method unknown to the client [auth_gssapi_client] (Connection: mariadb, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2054): SQLSTATE[HY000] [2054] The server requested authentication method unknown to the client [auth_gssapi_client] (Connection: mariadb, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}

[previous exception] [object] (PDOException(code: 2054): SQLSTATE[HY000] [2054] The server requested authentication method unknown to the client [auth_gssapi_client] at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#39 {main}
"} 
[2025-05-27 14:53:56] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357636) or (`reserved_at` <= 1748357546)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357636) or (`reserved_at` <= 1748357546)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:01] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357641) or (`reserved_at` <= 1748357551)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357641) or (`reserved_at` <= 1748357551)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:07] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357647) or (`reserved_at` <= 1748357557)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357647) or (`reserved_at` <= 1748357557)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:15] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357655) or (`reserved_at` <= 1748357565)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357655) or (`reserved_at` <= 1748357565)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:17] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.sessions' doesn't exist (Connection: mariadb, SQL: select * from `sessions` where `id` = 3Y5OeFcMeUqcHUxaLxDiMqJe6TwYvSxorkhRqLJk limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.sessions' doesn't exist (Connection: mariadb, SQL: select * from `sessions` where `id` = 3Y5OeFcMeUqcHUxaLxDiMqJe6TwYvSxorkhRqLJk limit 1) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('3Y5OeFcMeUqcHUx...')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('3Y5OeFcMeUqcHUx...')
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 F:\\Xampp\\htdocs\\new_order\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('F:\\\\Xampp\\\\htdocs...')
#53 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.sessions' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('3Y5OeFcMeUqcHUx...')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('3Y5OeFcMeUqcHUx...')
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 F:\\Xampp\\htdocs\\new_order\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('F:\\\\Xampp\\\\htdocs...')
#55 {main}
"} 
[2025-05-27 14:54:20] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357660) or (`reserved_at` <= 1748357570)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357660) or (`reserved_at` <= 1748357570)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:25] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357665) or (`reserved_at` <= 1748357575)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357665) or (`reserved_at` <= 1748357575)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:30] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357670) or (`reserved_at` <= 1748357580)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357670) or (`reserved_at` <= 1748357580)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:34] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357674) or (`reserved_at` <= 1748357584)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357674) or (`reserved_at` <= 1748357584)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:39] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357679) or (`reserved_at` <= 1748357589)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357679) or (`reserved_at` <= 1748357589)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:45] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = Es4YQyCtSYFMmz5YmFyj7ElIZMlDDgspT65sQs2C limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.sessions' doesn't exist (Connection: mysql, SQL: select * from `sessions` where `id` = Es4YQyCtSYFMmz5YmFyj7ElIZMlDDgspT65sQs2C limit 1) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('Es4YQyCtSYFMmz5...')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('Es4YQyCtSYFMmz5...')
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 F:\\Xampp\\htdocs\\new_order\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('F:\\\\Xampp\\\\htdocs...')
#53 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.sessions' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3027): Illuminate\\Database\\Query\\Builder->first(Array)
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('Es4YQyCtSYFMmz5...')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('Es4YQyCtSYFMmz5...')
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php(88): Illuminate\\Session\\Store->loadSession()
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(146): Illuminate\\Session\\Store->start()
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 F:\\Xampp\\htdocs\\new_order\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('F:\\\\Xampp\\\\htdocs...')
#55 {main}
"} 
[2025-05-27 14:54:47] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357687) or (`reserved_at` <= 1748357597)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357687) or (`reserved_at` <= 1748357597)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:54:54] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357694) or (`reserved_at` <= 1748357604)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357694) or (`reserved_at` <= 1748357604)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:00] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357700) or (`reserved_at` <= 1748357610)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357700) or (`reserved_at` <= 1748357610)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:06] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357706) or (`reserved_at` <= 1748357616)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357706) or (`reserved_at` <= 1748357616)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:11] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357711) or (`reserved_at` <= 1748357621)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357711) or (`reserved_at` <= 1748357621)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:16] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357716) or (`reserved_at` <= 1748357626)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357716) or (`reserved_at` <= 1748357626)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:23] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357723) or (`reserved_at` <= 1748357633)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357723) or (`reserved_at` <= 1748357633)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:28] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357727) or (`reserved_at` <= 1748357637)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357727) or (`reserved_at` <= 1748357637)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:32] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357732) or (`reserved_at` <= 1748357642)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357732) or (`reserved_at` <= 1748357642)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:37] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357737) or (`reserved_at` <= 1748357647)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357737) or (`reserved_at` <= 1748357647)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:42] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357742) or (`reserved_at` <= 1748357652)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357742) or (`reserved_at` <= 1748357652)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:46] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357746) or (`reserved_at` <= 1748357656)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357746) or (`reserved_at` <= 1748357656)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 14:55:51] local.ERROR: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357751) or (`reserved_at` <= 1748357661)) order by `id` asc limit 1 for update) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist (Connection: mariadb, SQL: select * from `jobs` where `queue` = default and ((`reserved_at` is null and `available_at` <= 1748357751) or (`reserved_at` <= 1748357661)) order by `id` asc limit 1 for update) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#30 {main}

[previous exception] [object] (PDOException(code: 42S02): SQLSTATE[42S02]: Base table or view not found: 1146 Table 'new_order.jobs' doesn't exist at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(248): Illuminate\\Database\\Query\\Builder->first()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(226): Illuminate\\Queue\\DatabaseQueue->getNextAvailableJob('default')
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\ManagesTransactions.php(32): Illuminate\\Queue\\DatabaseQueue->Illuminate\\Queue\\{closure}(Object(Illuminate\\Database\\MariaDbConnection))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php(225): Illuminate\\Database\\Connection->transaction(Object(Closure))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(350): Illuminate\\Queue\\DatabaseQueue->pop('default', 0)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(365): Illuminate\\Queue\\Worker->Illuminate\\Queue\\{closure}('default', 0)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Worker.php(326): Illuminate\\Queue\\Worker->getNextJob(Object(Illuminate\\Queue\\DatabaseQueue), 'default')
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(148): Illuminate\\Queue\\Worker->runNextJob('database', 'default', Object(Illuminate\\Queue\\WorkerOptions))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\WorkCommand.php(131): Illuminate\\Queue\\Console\\WorkCommand->runWorker('database', 'default')
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\WorkCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#32 {main}
"} 
[2025-05-27 18:32:28] local.ERROR: The process "F:\Xampp\php\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1" exceeded the timeout of 60 seconds. {"exception":"[object] (Symfony\\Component\\Process\\Exception\\ProcessTimedOutException(code: 0): The process \"F:\\Xampp\\php\\php.exe artisan queue:work --once --name=default --queue=default --backoff=0 --memory=128 --sleep=3 --tries=1\" exceeded the timeout of 60 seconds. at F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\process\\Process.php:1181)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\process\\Process.php(450): Symfony\\Component\\Process\\Process->checkTimeout()
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\process\\Process.php(251): Symfony\\Component\\Process\\Process->wait()
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(180): Symfony\\Component\\Process\\Process->run(Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Listener.php(91): Illuminate\\Queue\\Listener->runProcess(Object(Symfony\\Component\\Process\\Process), '128')
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Console\\ListenCommand.php(74): Illuminate\\Queue\\Listener->listen(NULL, 'default', Object(Illuminate\\Queue\\ListenerOptions))
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Queue\\Console\\ListenCommand->handle()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Queue\\Console\\ListenCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 F:\\Xampp\\htdocs\\new_order\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#19 {main}
"} 
[2025-05-28 09:46:44] local.ERROR: Call to undefined method App\Http\Controllers\Admin\UserController::middleware() {"userId":1,"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Admin\\UserController::middleware() at F:\\Xampp\\htdocs\\new_order\\app\\Http\\Controllers\\Admin\\UserController.php:15)
[stacktrace]
#0 [internal function]: App\\Http\\Controllers\\Admin\\UserController->__construct()
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 F:\\Xampp\\htdocs\\new_order\\app\\Http\\Middleware\\RoleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin')
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 F:\\Xampp\\htdocs\\new_order\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('F:\\\\Xampp\\\\htdocs...')
#59 {main}
"} 
[2025-05-28 09:47:25] local.ERROR: Call to undefined method App\Http\Controllers\Admin\UserController::middleware() {"userId":1,"exception":"[object] (Error(code: 0): Call to undefined method App\\Http\\Controllers\\Admin\\UserController::middleware() at F:\\Xampp\\htdocs\\new_order\\app\\Http\\Controllers\\Admin\\UserController.php:15)
[stacktrace]
#0 [internal function]: App\\Http\\Controllers\\Admin\\UserController->__construct()
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1062): ReflectionClass->newInstanceArgs(Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Contro...')
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Contro...', Array, true)
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Contro...', Array)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Contro...', Array)
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(286): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Contro...')
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\Route->getController()
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#11 F:\\Xampp\\htdocs\\new_order\\app\\Http\\Middleware\\RoleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\RoleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'admin')
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 F:\\Xampp\\htdocs\\new_order\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('F:\\\\Xampp\\\\htdocs...')
#59 {main}
"} 
[2025-05-28 09:55:42] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'products.p_o_id' in 'where clause' (Connection: mysql, SQL: select * from `products` where `products`.`p_o_id` in (1)) {"userId":2,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'products.p_o_id' in 'where clause' (Connection: mysql, SQL: select * from `products` where `products`.`p_o_id` in (1)) at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get(Array)
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(212): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(175): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(920): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation(Array, 'products', Object(Closure))
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(855): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations(Array)
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1095): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->paginate(10)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'paginate', Array)
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'paginate', Array)
#17 F:\\Xampp\\htdocs\\new_order\\app\\Http\\Controllers\\POController.php(22): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('paginate', Array)
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\POController->index()
#19 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\POController), 'index')
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#65 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#66 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#67 F:\\Xampp\\htdocs\\new_order\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#68 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('F:\\\\Xampp\\\\htdocs...')
#69 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'products.p_o_id' in 'where clause' at F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare('select * from `...')
#1 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#2 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#3 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3120): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3105): Illuminate\\Database\\Query\\Builder->runSelect()
#6 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3695): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3104): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get(Array)
#9 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(212): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(175): Illuminate\\Database\\Eloquent\\Relations\\Relation->get()
#12 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(920): Illuminate\\Database\\Eloquent\\Relations\\Relation->getEager()
#13 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(889): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation(Array, 'products', Object(Closure))
#14 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(855): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations(Array)
#15 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1095): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#16 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->paginate(10)
#17 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(52): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'paginate', Array)
#18 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(538): Illuminate\\Database\\Eloquent\\Relations\\Relation->forwardDecoratedCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'paginate', Array)
#19 F:\\Xampp\\htdocs\\new_order\\app\\Http\\Controllers\\POController.php(22): Illuminate\\Database\\Eloquent\\Relations\\Relation->__call('paginate', Array)
#20 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\POController->index()
#21 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\POController), 'index')
#22 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#23 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#24 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#35 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#43 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#44 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#45 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#46 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#47 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 F:\\Xampp\\htdocs\\new_order\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#70 F:\\Xampp\\htdocs\\new_order\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('F:\\\\Xampp\\\\htdocs...')
#71 {main}
"} 
