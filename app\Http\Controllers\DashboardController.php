<?php

namespace App\Http\Controllers;

use App\Models\PO;
use App\Models\Order;
use App\Models\Supply;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        if ($user->isAdmin()) {
            return $this->adminDashboard();
        } else {
            return $this->userDashboard();
        }
    }

    private function adminDashboard()
    {
        $stats = [
            'total_users' => User::where('role', 'user')->count(),
            'total_pos' => PO::count(),
            'total_orders' => Order::count(),
            'total_supplies' => Supply::count(),
            'pending_supplies' => Supply::where('status', 'pending')->count(),
            'issued_supplies' => Supply::where('status', 'issued')->count(),
            'total_payments' => Payment::sum('amount'),
            'pending_payments' => Payment::where('status', 'pending')->sum('amount'),
            'received_payments' => Payment::where('status', 'received')->sum('amount'),
        ];

        $recent_pos = PO::with(['user', 'products'])->latest()->take(5)->get();
        $recent_orders = Order::with(['user', 'po'])->latest()->take(5)->get();
        $pending_supplies = Supply::with(['order.po', 'product'])
            ->where('status', 'pending')
            ->latest()
            ->take(5)
            ->get();

        return view('dashboard.admin', compact('stats', 'recent_pos', 'recent_orders', 'pending_supplies'));
    }

    private function userDashboard()
    {
        $user = auth()->user();

        $stats = [
            'total_pos' => $user->pos()->count(),
            'total_orders' => $user->orders()->count(),
            'total_supplies' => Supply::whereHas('order', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })->count(),
            'pending_supplies' => Supply::whereHas('order', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })->where('status', 'pending')->count(),
            'issued_supplies' => Supply::whereHas('order', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })->where('status', 'issued')->count(),
            'total_payments' => Payment::whereHas('order', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })->sum('amount'),
            'pending_payments' => Payment::whereHas('order', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })->where('status', 'pending')->sum('amount'),
            'received_payments' => Payment::whereHas('order', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })->where('status', 'received')->sum('amount'),
        ];

        $recent_pos = $user->pos()->with('products')->latest()->take(5)->get();
        $recent_orders = $user->orders()->with('po')->latest()->take(5)->get();
        $pending_supplies = Supply::with(['order.po', 'product'])
            ->whereHas('order', function($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('status', 'pending')
            ->latest()
            ->take(5)
            ->get();

        return view('dashboard.user', compact('stats', 'recent_pos', 'recent_orders', 'pending_supplies'));
    }
}
