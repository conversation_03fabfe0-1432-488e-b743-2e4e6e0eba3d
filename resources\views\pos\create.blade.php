@extends('layouts.sidebar')

@section('title', 'Add New PO')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900">Add New Purchase Order</h2>
</div>

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <form action="{{ route('pos.store') }}" method="POST" enctype="multipart/form-data" id="po-form">
        @csrf
        
        <div class="px-4 py-5 sm:p-6">
            <!-- PO Details -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <label for="institution_name" class="block text-sm font-medium text-gray-700">Institution Name</label>
                    <input type="text" name="institution_name" id="institution_name" value="{{ old('institution_name') }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('institution_name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" name="email" id="email" value="{{ old('email') }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                    <input type="text" name="phone" id="phone" value="{{ old('phone') }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('phone')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="po_number" class="block text-sm font-medium text-gray-700">PO Number</label>
                    <input type="text" name="po_number" id="po_number" value="{{ old('po_number') }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('po_number')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="po_date" class="block text-sm font-medium text-gray-700">PO Date</label>
                    <input type="date" name="po_date" id="po_date" value="{{ old('po_date') }}" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>
                    @error('po_date')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <div>
                    <label for="po_image" class="block text-sm font-medium text-gray-700">PO Image</label>
                    <input type="file" name="po_image" id="po_image" accept="image/*" 
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                    @error('po_image')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="mb-6">
                <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                <textarea name="address" id="address" rows="3" 
                          class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" required>{{ old('address') }}</textarea>
                @error('address')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Products Section -->
            <div class="border-t border-gray-200 pt-6">
                <div class="flex justify-between items-center mb-4">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">Products</h3>
                        <p class="text-sm text-gray-500">Add one or more products to this PO</p>
                    </div>
                    <button type="button" id="add-product" class="bg-green-600 hover:bg-green-700 text-gray-900 font-bold py-3 px-6 rounded-lg shadow-md transition-all duration-200 hover:shadow-lg">
                        <i class="fas fa-plus mr-2"></i>Add Another Product
                    </button>
                </div>

                <!-- Instructions -->
                <div id="products-instructions" class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                        <p class="text-sm text-blue-700">
                            <strong>Instructions:</strong> Add products one by one using the "Add Another Product" button.
                            Each product will show its individual total, and the grand total will be calculated automatically.
                        </p>
                    </div>
                </div>

                <div id="products-container">
                    <!-- Products will be added here dynamically -->
                </div>

                <!-- Total Summary -->
                <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-medium text-gray-900">Total PO Value:</span>
                        <span id="total-value" class="text-xl font-bold text-blue-600">$0.00</span>
                    </div>
                    <div class="flex justify-between items-center mt-2">
                        <span class="text-sm text-gray-600">Total Items:</span>
                        <span id="total-items" class="text-sm font-medium text-gray-900">0</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
            <a href="{{ route('pos.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2">
                Cancel
            </a>
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-gray-800 font-bold py-2 px-4 rounded">
                Create PO
            </button>
        </div>
    </form>
</div>

<script>
let productIndex = 0;

document.getElementById('add-product').addEventListener('click', function() {
    addProductRow();
});

function addProductRow() {
    const container = document.getElementById('products-container');
    const instructions = document.getElementById('products-instructions');

    // Hide instructions after first product
    if (productIndex > 0) {
        instructions.style.display = 'none';
    }

    const productRow = document.createElement('div');
    productRow.className = 'grid grid-cols-1 md:grid-cols-5 gap-4 mb-4 p-4 border border-gray-200 rounded-lg product-row hover:border-gray-300 transition-colors';
    productRow.innerHTML = `
        <div>
            <label class="block text-sm font-medium text-gray-700">Product Name</label>
            <input type="text" name="products[${productIndex}][product_name]"
                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                   placeholder="Enter product name" required>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700">Price ($)</label>
            <input type="number" step="0.01" name="products[${productIndex}][price]"
                   class="price-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                   placeholder="0.00" onchange="calculateRowTotal(this)" required>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700">Quantity</label>
            <input type="number" name="products[${productIndex}][quantity]"
                   class="quantity-input mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                   placeholder="1" onchange="calculateRowTotal(this)" required>
        </div>
        <div>
            <label class="block text-sm font-medium text-gray-700">Total ($)</label>
            <input type="text" class="row-total mt-1 block w-full bg-gray-100 border-gray-300 rounded-md shadow-sm text-right font-medium"
                   readonly value="0.00">
        </div>
        <div class="flex items-end">
            <button type="button" onclick="removeProductRow(this)"
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded w-full transition-colors"
                    title="Remove this product">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    container.appendChild(productRow);
    productIndex++;
    updateGrandTotal();

    // Focus on the product name input of the newly added row
    const newRow = container.lastElementChild;
    const productNameInput = newRow.querySelector('input[name*="product_name"]');
    if (productNameInput) {
        productNameInput.focus();
    }
}

function removeProductRow(button) {
    button.closest('.product-row').remove();
    updateGrandTotal();
}

function calculateRowTotal(input) {
    const row = input.closest('.product-row');
    const priceInput = row.querySelector('.price-input');
    const quantityInput = row.querySelector('.quantity-input');
    const totalInput = row.querySelector('.row-total');

    const price = parseFloat(priceInput.value) || 0;
    const quantity = parseInt(quantityInput.value) || 0;
    const total = price * quantity;

    totalInput.value = total.toFixed(2);
    updateGrandTotal();
}

function updateGrandTotal() {
    const rows = document.querySelectorAll('.product-row');
    let grandTotal = 0;
    let totalItems = 0;

    rows.forEach(row => {
        const rowTotal = parseFloat(row.querySelector('.row-total').value) || 0;
        const quantity = parseInt(row.querySelector('.quantity-input').value) || 0;
        grandTotal += rowTotal;
        totalItems += quantity;
    });

    document.getElementById('total-value').textContent = '$' + grandTotal.toFixed(2);
    document.getElementById('total-items').textContent = totalItems;

    // Update button text to show product count
    const addButton = document.getElementById('add-product');
    const productCount = rows.length;
    if (productCount === 0) {
        addButton.innerHTML = '<i class="fas fa-plus mr-2"></i>Add First Product';
    } else {
        addButton.innerHTML = `<i class="fas fa-plus mr-2"></i>Add Another Product (${productCount} added)`;
    }
}

// Form validation
document.getElementById('po-form').addEventListener('submit', function(e) {
    const rows = document.querySelectorAll('.product-row');
    if (rows.length === 0) {
        e.preventDefault();
        alert('Please add at least one product to the PO.');
        return false;
    }

    let hasEmptyFields = false;
    rows.forEach(row => {
        const inputs = row.querySelectorAll('input[required]');
        inputs.forEach(input => {
            if (!input.value.trim()) {
                hasEmptyFields = true;
                input.classList.add('border-red-500');
            } else {
                input.classList.remove('border-red-500');
            }
        });
    });

    if (hasEmptyFields) {
        e.preventDefault();
        alert('Please fill in all required fields for all products.');
        return false;
    }
});

// Add initial product row
addProductRow();
</script>
@endsection
