<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\PO;
use App\Models\Supply;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();

        if ($user->isAdmin()) {
            $orders = Order::with(['user', 'po', 'orderItems.product'])->latest()->paginate(10);
        } else {
            $orders = $user->orders()->with(['po', 'orderItems.product'])->latest()->paginate(10);
        }

        return view('orders.index', compact('orders'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $user = auth()->user();

        if ($user->isAdmin()) {
            $pos = PO::with('products')->get();
        } else {
            $pos = $user->pos()->with('products')->get();
        }

        return view('orders.create', compact('pos'));
    }

    /**
     * Create order from specific PO
     */
    public function createFromPO(PO $po)
    {
        // Check if user can view this PO
        if (!auth()->user()->isAdmin() && auth()->id() !== $po->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $po->load('products');

        return view('orders.create-from-po', compact('po'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'po_id' => 'required|exists:pos,id',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity_supplied' => 'required|integer|min:1',
        ]);

        $po = PO::findOrFail($request->po_id);

        // Check if user can view this PO
        if (!auth()->user()->isAdmin() && auth()->id() !== $po->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $order = Order::create([
            'user_id' => auth()->id(),
            'po_id' => $request->po_id,
        ]);

        foreach ($request->items as $itemData) {
            $product = $po->products()->findOrFail($itemData['product_id']);

            // Check if quantity doesn't exceed remaining quantity
            $remainingQuantity = $product->remaining_quantity;
            if ($itemData['quantity_supplied'] > $remainingQuantity) {
                return back()->withErrors([
                    'items' => "Quantity for {$product->product_name} exceeds remaining quantity ({$remainingQuantity})"
                ]);
            }

            $orderItem = OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $itemData['product_id'],
                'quantity_supplied' => $itemData['quantity_supplied'],
                'price' => $product->price,
            ]);

            // Create supply record
            Supply::create([
                'order_id' => $order->id,
                'product_id' => $itemData['product_id'],
                'quantity_supplied' => $itemData['quantity_supplied'],
                'status' => 'pending',
            ]);
        }

        return redirect()->route('orders.show', $order)->with('success', 'Order created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order)
    {
        // Check if user can view this order
        if (!auth()->user()->isAdmin() && auth()->id() !== $order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $order->load(['po', 'orderItems.product', 'supplies', 'payments']);

        return view('orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Order $order)
    {
        // Check if user can edit this order
        if (!auth()->user()->isAdmin() && auth()->id() !== $order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $order->load(['po.products', 'orderItems']);

        return view('orders.edit', compact('order'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Order $order)
    {
        // Check if user can update this order
        if (!auth()->user()->isAdmin() && auth()->id() !== $order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $request->validate([
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity_supplied' => 'required|integer|min:1',
        ]);

        // Delete existing order items and supplies
        $order->orderItems()->delete();
        $order->supplies()->delete();

        foreach ($request->items as $itemData) {
            $product = $order->po->products()->findOrFail($itemData['product_id']);

            $orderItem = OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $itemData['product_id'],
                'quantity_supplied' => $itemData['quantity_supplied'],
                'price' => $product->price,
            ]);

            Supply::create([
                'order_id' => $order->id,
                'product_id' => $itemData['product_id'],
                'quantity_supplied' => $itemData['quantity_supplied'],
                'status' => 'pending',
            ]);
        }

        return redirect()->route('orders.show', $order)->with('success', 'Order updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Order $order)
    {
        // Check if user can delete this order
        if (!auth()->user()->isAdmin() && auth()->id() !== $order->user_id) {
            abort(403, 'Unauthorized access.');
        }

        $order->delete();

        return redirect()->route('orders.index')->with('success', 'Order deleted successfully!');
    }
}
