@extends('layouts.sidebar')

@section('title', 'Create New Order')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-900">Create New Order</h2>
    <p class="text-gray-600">Select a PO to create an order from</p>
</div>

<div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:p-6">
        @if($pos->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($pos as $po)
                    <div class="border border-gray-200 rounded-lg p-6 hover:border-gray-300 transition-colors">
                        <div class="flex justify-between items-start mb-4">
                            <div>
                                <h3 class="text-lg font-medium text-gray-900">{{ $po->po_number }}</h3>
                                <p class="text-sm text-gray-500">{{ $po->institution_name }}</p>
                            </div>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ $po->products->count() }} items
                            </span>
                        </div>
                        
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">PO Date:</span>
                                <span class="text-gray-900">{{ $po->po_date->format('M d, Y') }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Total Value:</span>
                                <span class="text-gray-900 font-medium">${{ number_format($po->total_value, 2) }}</span>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-600">Orders Created:</span>
                                <span class="text-gray-900">{{ $po->orders->count() }}</span>
                            </div>
                        </div>
                        
                        <!-- Products Preview -->
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-900 mb-2">Products:</h4>
                            <div class="space-y-1">
                                @foreach($po->products->take(3) as $product)
                                    <div class="text-xs text-gray-600">
                                        {{ $product->product_name }} ({{ $product->quantity }} × ${{ number_format($product->price, 2) }})
                                    </div>
                                @endforeach
                                @if($po->products->count() > 3)
                                    <div class="text-xs text-gray-500">
                                        +{{ $po->products->count() - 3 }} more items
                                    </div>
                                @endif
                            </div>
                        </div>
                        
                        <div class="flex space-x-2">
                            <a href="{{ route('orders.create.from.po', $po) }}"
                               class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded text-sm font-medium">
                                Create Order
                            </a>
                            <a href="{{ route('pos.show', $po) }}"
                               class="flex-1 bg-gray-600 hover:bg-gray-700 text-white text-center py-2 px-4 rounded text-sm font-medium">
                                View PO
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-12">
                <i class="fas fa-file-invoice text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No POs Available</h3>
                <p class="text-gray-500 mb-4">You need to create a PO first before you can create orders.</p>
                <a href="{{ route('pos.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-plus mr-2"></i>Create New PO
                </a>
            </div>
        @endif
    </div>
</div>

@if($pos->count() > 0)
<div class="mt-6 bg-blue-50 border border-blue-200 rounded-md p-4">
    <div class="flex">
        <div class="flex-shrink-0">
            <i class="fas fa-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">How to Create Orders</h3>
            <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                    <li>Select a PO from the list above</li>
                    <li>Choose which products and quantities to supply</li>
                    <li>The system will automatically create supply records</li>
                    <li>You can create multiple orders from the same PO</li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
