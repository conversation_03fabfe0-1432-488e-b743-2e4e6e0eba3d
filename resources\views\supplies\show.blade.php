@extends('layouts.sidebar')

@section('title', 'Supply Details')

@section('content')
<div class="mb-6 flex justify-between items-center">
    <h2 class="text-2xl font-bold text-gray-900">Supply Details</h2>
    <div class="flex space-x-2">
        @if(auth()->user()->isAdmin() || auth()->id() === $supply->order->user_id)
            <a href="{{ route('supplies.edit', $supply) }}" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-edit mr-2"></i>Edit
            </a>
        @endif
        <a href="{{ route('supplies.index') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            <i class="fas fa-arrow-left mr-2"></i>Back
        </a>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Supply Information -->
    <div class="lg:col-span-2">
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Supply Information</h3>
            </div>
            <div class="border-t border-gray-200">
                <dl>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Product</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $supply->product->product_name }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Order ID</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <a href="{{ route('orders.show', $supply->order) }}" class="text-blue-600 hover:text-blue-500">
                                #{{ $supply->order->id }}
                            </a>
                        </dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">PO Number</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <a href="{{ route('pos.show', $supply->order->po) }}" class="text-blue-600 hover:text-blue-500">
                                {{ $supply->order->po->po_number }}
                            </a>
                        </dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Quantity Supplied</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $supply->quantity_supplied }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Unit Price</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">${{ number_format($supply->product->price, 2) }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Total Value</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">${{ number_format($supply->value, 2) }}</dd>
                    </div>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $supply->status === 'issued' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ ucfirst($supply->status) }}
                            </span>
                        </dd>
                    </div>
                    @if(auth()->user()->isAdmin())
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Created by</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $supply->order->user->name }}</dd>
                    </div>
                    @endif
                </dl>
            </div>
        </div>

        <!-- DC Information -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Delivery Challan (DC) Information</h3>
            </div>
            <div class="border-t border-gray-200">
                <dl>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">DC Number</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $supply->dc_number ?: 'Not provided' }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">DC Date</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $supply->dc_date ? $supply->dc_date->format('M d, Y') : 'Not provided' }}</dd>
                    </div>
                    @if($supply->dc_image_path)
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">DC Image</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <a href="{{ asset('storage/' . $supply->dc_image_path) }}" target="_blank" class="text-blue-600 hover:text-blue-500">
                                <i class="fas fa-image mr-2"></i>View DC Image
                            </a>
                        </dd>
                    </div>
                    @endif
                </dl>
            </div>
        </div>

        <!-- Invoice Information -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Invoice Information</h3>
            </div>
            <div class="border-t border-gray-200">
                <dl>
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Invoice Number</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $supply->invoice_number ?: 'Not provided' }}</dd>
                    </div>
                    <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Invoice Date</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ $supply->invoice_date ? $supply->invoice_date->format('M d, Y') : 'Not provided' }}</dd>
                    </div>
                    @if($supply->invoice_image_path)
                    <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                        <dt class="text-sm font-medium text-gray-500">Invoice Image</dt>
                        <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                            <a href="{{ asset('storage/' . $supply->invoice_image_path) }}" target="_blank" class="text-blue-600 hover:text-blue-500">
                                <i class="fas fa-receipt mr-2"></i>View Invoice Image
                            </a>
                        </dd>
                    </div>
                    @endif
                </dl>
            </div>
        </div>
    </div>

    <!-- Summary & Actions -->
    <div class="lg:col-span-1">
        <!-- Summary Card -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Summary</h3>
            </div>
            <div class="border-t border-gray-200 px-4 py-5">
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Product:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $supply->product->product_name }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Quantity:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $supply->quantity_supplied }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Value:</span>
                        <span class="text-sm font-medium text-gray-900">${{ number_format($supply->value, 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Status:</span>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $supply->status === 'issued' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                            {{ ucfirst($supply->status) }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white shadow overflow-hidden sm:rounded-lg mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
            </div>
            <div class="border-t border-gray-200 px-4 py-5">
                <div class="space-y-3">
                    @if(auth()->user()->isAdmin() || auth()->id() === $supply->order->user_id)
                        <form action="{{ route('supplies.update.status', $supply) }}" method="POST">
                            @csrf
                            @method('PATCH')
                            <input type="hidden" name="status" value="{{ $supply->status === 'pending' ? 'issued' : 'pending' }}">
                            <button type="submit" class="block w-full bg-purple-600 hover:bg-purple-700 text-white text-center py-2 px-4 rounded text-sm font-medium">
                                <i class="fas fa-toggle-{{ $supply->status === 'pending' ? 'on' : 'off' }} mr-2"></i>
                                Mark as {{ $supply->status === 'pending' ? 'Issued' : 'Pending' }}
                            </button>
                        </form>
                    @endif
                    
                    <a href="{{ route('orders.show', $supply->order) }}" 
                       class="block w-full bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded text-sm font-medium">
                        <i class="fas fa-shopping-cart mr-2"></i>View Order
                    </a>
                    
                    <a href="{{ route('pos.show', $supply->order->po) }}" 
                       class="block w-full bg-green-600 hover:bg-green-700 text-white text-center py-2 px-4 rounded text-sm font-medium">
                        <i class="fas fa-file-invoice mr-2"></i>View Original PO
                    </a>
                </div>
            </div>
        </div>

        <!-- Status Change -->
        @if(auth()->user()->isAdmin() || auth()->id() === $supply->order->user_id)
        <div class="bg-white shadow overflow-hidden sm:rounded-lg">
            <div class="px-4 py-5 sm:px-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Status Information</h3>
            </div>
            <div class="border-t border-gray-200 px-4 py-5">
                <div class="text-sm text-gray-600">
                    <p class="mb-2"><strong>Current Status:</strong> {{ ucfirst($supply->status) }}</p>
                    <p class="mb-2"><strong>Created:</strong> {{ $supply->created_at->format('M d, Y H:i') }}</p>
                    <p><strong>Last Updated:</strong> {{ $supply->updated_at->format('M d, Y H:i') }}</p>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
