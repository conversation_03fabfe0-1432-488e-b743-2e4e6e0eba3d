<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\POController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\SupplyController;
use App\Http\Controllers\PaymentController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('login');
});

Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // PO Routes
    Route::resource('pos', POController::class);

    // Order Routes
    Route::resource('orders', OrderController::class);
    Route::get('orders/create/{po}', [OrderController::class, 'createFromPO'])->name('orders.create.from.po');

    // Supply Routes
    Route::resource('supplies', SupplyController::class);
    Route::patch('supplies/{supply}/status', [SupplyController::class, 'updateStatus'])->name('supplies.update.status');

    // Payment Routes
    Route::resource('payments', PaymentController::class);
    Route::patch('payments/{payment}/status', [PaymentController::class, 'updateStatus'])->name('payments.update.status');
});

// Admin only routes
Route::middleware(['auth', 'role:admin'])->group(function () {
    Route::get('/admin/users', function () {
        return view('admin.users.index');
    })->name('admin.users.index');
});

require __DIR__.'/auth.php';
